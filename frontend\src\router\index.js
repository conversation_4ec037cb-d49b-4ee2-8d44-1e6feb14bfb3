import { createRouter, createWebHistory } from 'vue-router'
import { isAuthenticated } from '@/services/api'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/chargers',
      name: 'chargers',
      component: () => import('../views/ChargersView.vue'),
      meta: { requiresAuth: true, allowGuest: true }
    },
    {
      path: '/chargers/new',
      name: 'charger-create',
      component: () => import('../views/ChargerFormView.vue'),
      meta: { requiresAuth: true, allowGuest: false }
    },
    {
      path: '/chargers/:id/edit',
      name: 'charger-edit',
      component: () => import('../views/ChargerFormView.vue'),
      meta: { requiresAuth: true, allowGuest: false },
      props: true
    },
    {
      path: '/map',
      name: 'map',
      component: () => import('../views/MapView.vue'),
      meta: { requiresAuth: true, allowGuest: true }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { requiresAuth: true, allowGuest: true }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFoundView.vue')
    }
  ]
})

// Navigation guards
router.beforeEach((to, from, next) => {
  const authenticated = isAuthenticated()
  const isGuestMode = localStorage.getItem('isGuest') === 'true'
  const hasAccess = authenticated || isGuestMode

  if (to.meta.requiresAuth) {
    if (!hasAccess) {
      // No authentication at all - redirect to login
      next('/login')
    } else if (isGuestMode && to.meta.allowGuest === false) {
      // Guest trying to access restricted area - redirect to chargers
      next('/chargers')
    } else {
      // Authenticated user or guest with access
      next()
    }
  } else if (to.meta.requiresGuest && hasAccess) {
    // Already authenticated/guest trying to access login/register
    next('/chargers')
  } else {
    next()
  }
})

export default router
