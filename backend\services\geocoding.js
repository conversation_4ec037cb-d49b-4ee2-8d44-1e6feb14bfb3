const axios = require('axios');

class GeocodingService {
  constructor() {
    this.apiKey = process.env.OPENWEATHERMAP_API_KEY;
    this.baseUrl = 'http://api.openweathermap.org/geo/1.0';
    
    if (!this.apiKey) {
      console.warn('OpenWeatherMap API key not found. Geocoding features will be disabled.');
    }
  }

  /**
   * Search for locations by name with autocomplete suggestions
   * @param {string} query - Location name to search for
   * @param {number} limit - Maximum number of results (default: 5)
   * @returns {Promise<Array>} Array of location suggestions
   */
  async searchLocations(query, limit = 5) {
    if (!this.apiKey) {
      throw new Error('OpenWeatherMap API key not configured');
    }

    if (!query || query.trim().length < 2) {
      return [];
    }

    try {
      const response = await axios.get(`${this.baseUrl}/direct`, {
        params: {
          q: query.trim(),
          limit: Math.min(limit, 10), // OpenWeatherMap allows max 10
          appid: this.apiKey
        },
        timeout: 5000 // 5 second timeout
      });

      return response.data.map(location => ({
        name: location.name,
        country: location.country,
        state: location.state || null,
        latitude: location.lat,
        longitude: location.lon,
        displayName: this.formatDisplayName(location)
      }));
    } catch (error) {
      console.error('Geocoding search error:', error.message);
      
      if (error.response) {
        // API error response
        throw new Error(`Geocoding service error: ${error.response.status}`);
      } else if (error.code === 'ECONNABORTED') {
        // Timeout error
        throw new Error('Geocoding service timeout');
      } else {
        // Network or other error
        throw new Error('Geocoding service unavailable');
      }
    }
  }

  /**
   * Get coordinates for a specific location name
   * @param {string} locationName - Exact location name
   * @returns {Promise<Object|null>} Location object with coordinates or null
   */
  async getCoordinates(locationName) {
    if (!this.apiKey) {
      throw new Error('OpenWeatherMap API key not configured');
    }

    try {
      const results = await this.searchLocations(locationName, 1);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Reverse geocoding - get location name from coordinates
   * @param {number} latitude - Latitude coordinate
   * @param {number} longitude - Longitude coordinate
   * @returns {Promise<Object|null>} Location object or null
   */
  async reverseGeocode(latitude, longitude) {
    if (!this.apiKey) {
      throw new Error('OpenWeatherMap API key not configured');
    }

    if (!latitude || !longitude) {
      throw new Error('Latitude and longitude are required');
    }

    try {
      const response = await axios.get(`${this.baseUrl}/reverse`, {
        params: {
          lat: latitude,
          lon: longitude,
          limit: 1,
          appid: this.apiKey
        },
        timeout: 5000
      });

      if (response.data.length > 0) {
        const location = response.data[0];
        return {
          name: location.name,
          country: location.country,
          state: location.state || null,
          latitude: location.lat,
          longitude: location.lon,
          displayName: this.formatDisplayName(location)
        };
      }

      return null;
    } catch (error) {
      console.error('Reverse geocoding error:', error.message);
      throw new Error('Reverse geocoding failed');
    }
  }

  /**
   * Format display name for location
   * @param {Object} location - Location object from API
   * @returns {string} Formatted display name
   */
  formatDisplayName(location) {
    let displayName = location.name;
    
    if (location.state) {
      displayName += `, ${location.state}`;
    }
    
    if (location.country) {
      displayName += `, ${location.country}`;
    }
    
    return displayName;
  }

  /**
   * Validate coordinates
   * @param {number} latitude - Latitude to validate
   * @param {number} longitude - Longitude to validate
   * @returns {boolean} True if coordinates are valid
   */
  validateCoordinates(latitude, longitude) {
    return (
      typeof latitude === 'number' &&
      typeof longitude === 'number' &&
      latitude >= -90 &&
      latitude <= 90 &&
      longitude >= -180 &&
      longitude <= 180
    );
  }
}

module.exports = new GeocodingService();
